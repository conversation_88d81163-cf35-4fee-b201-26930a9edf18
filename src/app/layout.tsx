import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Analytics } from "@vercel/analytics/next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    template: "%s | Glide",
    default: "Glide - AI-Powered PR Analysis"
  },
  description: "Understand code changes effortlessly. Get AI-powered walkthroughs of GitHub Pull Requests with clear explanations, use case analysis, and step-by-step breakdowns.",
  keywords: ["GitHub", "Pull Request", "PR", "Code Review", "AI", "Analysis", "Walkthrough", "Developer Tools"],
  authors: [{ name: "Glide", url: "https://useglide.ai" }],
  creator: "Glide",
  publisher: "Glide",
  metadataBase: new URL("https://useglide.ai"),

  openGraph: {
    title: "Glide - AI-Powered PR Analysis",
    description: "Understand code changes effortlessly. Get AI-powered walkthroughs of GitHub Pull Requests.",
    url: "https://useglide.ai",
    siteName: "Glide",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Glide - AI-Powered PR Analysis",
    description: "Understand code changes effortlessly. Get AI-powered walkthroughs of GitHub Pull Requests.",
    creator: "@useglideai",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Replace with actual verification code
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
        <Analytics />
      </body>
    </html>
  );
}
