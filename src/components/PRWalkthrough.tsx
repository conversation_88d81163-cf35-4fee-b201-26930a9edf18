'use client';

import { useState, useEffect, useCallback } from 'react';
import { PRWalkthroughResult, AnalysisProgress } from '../types';
import AnalysisCard from './AnalysisCard';
import ProgressBar from './ProgressBar';
import { CheckIcon, ArrowRightIcon, ArrowLeftIcon } from './Icons';

interface PRWalkthroughProps {
  org: string;
  repo: string;
  pr_num: string;
}

type WalkthroughStep = 'overview' | 'usecase' | 'summary' | 'changes';

interface StepProgress {
  overview: boolean;
  usecase: boolean;
  summary: boolean;
  changes: { [groupId: string]: boolean };
}

export default function PRWalkthrough({ org, repo, pr_num }: PRWalkthroughProps) {
  const [walkthrough, setWalkthrough] = useState<PRWalkthroughResult | null>(null);
  const [loading, setLoading] = useState(true); // Start loading immediately
  const [error, setError] = useState<string | null>(null);
  const [useSteppedView, setUseSteppedView] = useState(true); // Default to stepped view
  const [currentStep, setCurrentStep] = useState<WalkthroughStep>('overview');
  const [currentChangeIndex, setCurrentChangeIndex] = useState(0);
  const [stepProgress, setStepProgress] = useState<StepProgress>({
    overview: false,
    usecase: false,
    summary: false,
    changes: {},
  });
  const [progress, setProgress] = useState<AnalysisProgress>({
    stage: 'fetching',
    message: 'Initializing walkthrough...',
    progress: 0,
  });

  const startAnalysis = useCallback(async () => {
    setLoading(true);
    setError(null);
    setWalkthrough(null);

    try {
      // Start with fetching stage
      setProgress({
        stage: 'fetching',
        message: 'Fetching PR data from GitHub...',
        progress: 10,
      });

      // Simulate progress during GitHub fetch
      const fetchProgressTimer = setTimeout(() => {
        setProgress({
          stage: 'fetching',
          message: 'Processing PR files and diffs...',
          progress: 30,
        });
      }, 1000);

      // Transition to analyzing stage after reasonable GitHub fetch time
      const analyzeProgressTimer = setTimeout(() => {
        setProgress({
          stage: 'analyzing',
          message: 'Analyzing PR with AI...',
          progress: 50,
        });
      }, 3000);

      // Further AI analysis progress
      const aiProgressTimer = setTimeout(() => {
        setProgress({
          stage: 'analyzing',
          message: 'Creating walkthrough structure...',
          progress: 75,
        });
      }, 8000);

      const response = await fetch('/api/analyze-pr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ org, repo, pr_num }),
      });

      // Clear all timers since the request completed
      clearTimeout(fetchProgressTimer);
      clearTimeout(analyzeProgressTimer);
      clearTimeout(aiProgressTimer);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Analysis failed');
      }

      // Final progress update before completion
      setProgress({
        stage: 'analyzing',
        message: 'Finalizing walkthrough...',
        progress: 95,
      });

      const result = await response.json();

      setProgress({
        stage: 'complete',
        message: 'Walkthrough complete!',
        progress: 100,
      });

      setWalkthrough(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      setProgress({
        stage: 'error',
        message: 'Analysis failed',
        progress: 0,
      });
    } finally {
      setLoading(false);
    }
  }, [org, repo, pr_num]);

  // Auto-start analysis when component mounts
  useEffect(() => {
    startAnalysis();
  }, [startAnalysis]); // Re-run if startAnalysis changes



  const getImportanceIcon = (importance: string) => {
    switch (importance) {
      case 'critical': return '🔴';
      case 'important': return '🟡';
      case 'supporting': return '🔵';
      case 'minor': return '⚪';
      default: return '⚫';
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'core_logic': return '⚙️';
      case 'configuration': return '🔧';
      case 'tests': return '🧪';
      case 'documentation': return '📝';
      case 'mechanical': return '🔄';
      case 'setup': return '🏗️';
      default: return '📄';
    }
  };

  return (
    <>
      {loading && (
        <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
          <div className="mb-4">
            <h2 className="text-xl font-semibold">PR Walkthrough</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Creating an AI-powered walkthrough of this PR...
            </p>
          </div>
          <ProgressBar
            progress={progress.progress}
            message={progress.message}
            stage={progress.stage}
          />
        </div>
      )}

      {error && (
        <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
          <div className="mb-4">
            <h2 className="text-xl font-semibold">PR Walkthrough</h2>
          </div>
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700">Error: {error}</p>
          </div>
        </div>
      )}

      {walkthrough && (
        <>
          {useSteppedView ? (
            <SteppedWalkthroughView
              walkthrough={walkthrough}
              currentStep={currentStep}
              setCurrentStep={setCurrentStep}
              currentChangeIndex={currentChangeIndex}
              setCurrentChangeIndex={setCurrentChangeIndex}
              stepProgress={stepProgress}
              setStepProgress={setStepProgress}
              onSwitchToFullView={() => setUseSteppedView(false)}
            />
          ) : (
            <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">PR Walkthrough</h2>
                <button
                  onClick={() => setUseSteppedView(true)}
                  className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Switch to Step-by-Step
                </button>
              </div>

              {/* Full view content - keeping the original detailed view */}
              <div className="space-y-6">
                {/* Use Case Section */}
                <AnalysisCard title="What's the Use Case?" className="border-l-4 border-l-blue-500">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-600 mr-2">Type:</span>
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">
                        {walkthrough.useCase.type.replace('_', ' ')}
                      </span>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300">
                      {walkthrough.useCase.description}
                    </p>

                    {walkthrough.useCase.concreteExample && (
                      <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Concrete Example:
                        </p>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {walkthrough.useCase.concreteExample}
                        </p>
                      </div>
                    )}

                    {walkthrough.useCase.beforeAfter && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                          <p className="text-sm font-medium text-red-700 dark:text-red-400 mb-1">
                            Before:
                          </p>
                          <p className="text-sm text-gray-700 dark:text-gray-300">
                            {walkthrough.useCase.beforeAfter.before}
                          </p>
                        </div>
                        <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                          <p className="text-sm font-medium text-green-700 dark:text-green-400 mb-1">
                            After:
                          </p>
                          <p className="text-sm text-gray-700 dark:text-gray-300">
                            {walkthrough.useCase.beforeAfter.after}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </AnalysisCard>

                {/* Summary */}
                <div className="border-b pb-4">
                  <h3 className="text-lg font-medium mb-2">Summary</h3>
                  <p className="text-gray-700 dark:text-gray-300">{walkthrough.summary}</p>
                </div>

                {/* Change Groups */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Changes Breakdown</h3>
                  <div className="space-y-4">
                    {walkthrough.reviewOrder.map((groupId, index) => {
                      const group = walkthrough.changeGroups.find(g => g.id === groupId);
                      if (!group) return null;

                      return (
                        <AnalysisCard
                          key={group.id}
                          title={`${index + 1}. ${group.title}`}
                          className="border-l-4 border-l-gray-300"
                        >
                          <div className="space-y-3">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center">
                                <span className="mr-1">{getImportanceIcon(group.importance)}</span>
                                <span className="text-sm text-gray-600">{group.importance}</span>
                              </div>
                              <div className="flex items-center">
                                <span className="mr-1">{getChangeTypeIcon(group.changeType)}</span>
                                <span className="text-sm text-gray-600">{group.changeType.replace('_', ' ')}</span>
                              </div>
                            </div>

                            <p className="text-gray-700 dark:text-gray-300">
                              {group.description}
                            </p>

                            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                                Explanation:
                              </p>
                              <p className="text-sm text-gray-700 dark:text-gray-300">
                                {group.explanation}
                              </p>
                            </div>

                            <div>
                              <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                                Files ({group.files.length}):
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {group.files.map((file, fileIndex) => (
                                  <span
                                    key={fileIndex}
                                    className="px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs"
                                  >
                                    {file}
                                  </span>
                                ))}
                              </div>
                            </div>

                            {/* Diff Hunks - truncated for brevity */}
                            {group.diffHunks.length > 0 && (
                              <div>
                                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                                  Code Changes:
                                </p>
                                <div className="text-sm text-gray-500">
                                  {group.diffHunks.length} diff hunk(s) - Switch to step-by-step view to see details
                                </div>
                              </div>
                            )}

                            {group.reviewNotes && group.reviewNotes.length > 0 && (
                              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md">
                                <p className="text-sm font-medium text-yellow-700 dark:text-yellow-400 mb-1">
                                  Review Notes:
                                </p>
                                <ul className="text-sm text-gray-700 dark:text-gray-300 list-disc list-inside">
                                  {group.reviewNotes.map((note, noteIndex) => (
                                    <li key={noteIndex}>{note}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </AnalysisCard>
                      );
                    })}
                  </div>
                </div>

                <div className="text-xs text-gray-500 border-t pt-2">
                  Walkthrough created at: {new Date(walkthrough.analysisTimestamp).toLocaleString()}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
}

// Stepped Walkthrough Component
interface SteppedWalkthroughViewProps {
  walkthrough: PRWalkthroughResult;
  currentStep: WalkthroughStep;
  setCurrentStep: (step: WalkthroughStep) => void;
  currentChangeIndex: number;
  setCurrentChangeIndex: (index: number) => void;
  stepProgress: StepProgress;
  setStepProgress: (progress: StepProgress | ((prev: StepProgress) => StepProgress)) => void;
  onSwitchToFullView: () => void;
}

function SteppedWalkthroughView({
  walkthrough,
  currentStep,
  setCurrentStep,
  currentChangeIndex,
  setCurrentChangeIndex,
  stepProgress,
  setStepProgress,
  onSwitchToFullView
}: SteppedWalkthroughViewProps) {
  const totalChangeGroups = walkthrough.reviewOrder.length;
  const currentChangeGroup = walkthrough.changeGroups.find(
    g => g.id === walkthrough.reviewOrder[currentChangeIndex]
  );

  const markStepComplete = (step: WalkthroughStep, groupId?: string) => {
    setStepProgress(prev => {
      if (step === 'changes' && groupId) {
        return {
          ...prev,
          changes: { ...prev.changes, [groupId]: true }
        };
      }
      return { ...prev, [step]: true };
    });
  };

  const getStepIcon = (step: WalkthroughStep, groupId?: string) => {
    const isComplete = step === 'changes' && groupId
      ? stepProgress.changes[groupId]
      : stepProgress[step as keyof Omit<StepProgress, 'changes'>];

    return isComplete ? (
      <CheckIcon className="w-5 h-5 text-green-600" />
    ) : (
      <div className="w-5 h-5 rounded-full border-2 border-gray-300" />
    );
  };

  const nextStep = () => {
    if (currentStep === 'overview') {
      setCurrentStep('usecase');
    } else if (currentStep === 'usecase') {
      setCurrentStep('summary');
    } else if (currentStep === 'summary') {
      setCurrentStep('changes');
      setCurrentChangeIndex(0);
    } else if (currentStep === 'changes') {
      if (currentChangeIndex < totalChangeGroups - 1) {
        setCurrentChangeIndex(currentChangeIndex + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep === 'changes') {
      if (currentChangeIndex > 0) {
        setCurrentChangeIndex(currentChangeIndex - 1);
      } else {
        setCurrentStep('summary');
      }
    } else if (currentStep === 'summary') {
      setCurrentStep('usecase');
    } else if (currentStep === 'usecase') {
      setCurrentStep('overview');
    }
  };

  const canGoNext = () => {
    if (currentStep === 'overview') return stepProgress.overview;
    if (currentStep === 'usecase') return stepProgress.usecase;
    if (currentStep === 'summary') return stepProgress.summary;
    if (currentStep === 'changes') {
      const currentGroupId = walkthrough.reviewOrder[currentChangeIndex];
      return stepProgress.changes[currentGroupId] && currentChangeIndex < totalChangeGroups - 1;
    }
    return false;
  };

  const canGoPrev = () => {
    if (currentStep === 'overview') return false;
    if (currentStep === 'changes' && currentChangeIndex > 0) return true;
    return true; // usecase, summary, or changes with index 0 can go back
  };

  const isLastStep = () => {
    return currentStep === 'changes' && currentChangeIndex === totalChangeGroups - 1;
  };

  return (
    <div className="border rounded-lg p-6 bg-white dark:bg-gray-800">
      {/* Progress Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">PR Walkthrough</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={onSwitchToFullView}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              View All at Once
            </button>
            <div className="text-sm text-gray-500">
              {currentStep === 'changes'
                ? `Step ${currentChangeIndex + 4} of ${totalChangeGroups + 3}`
                : `Step ${currentStep === 'overview' ? 1 : currentStep === 'usecase' ? 2 : 3} of ${totalChangeGroups + 3}`
              }
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{
              width: `${currentStep === 'overview' ? 10 :
                      currentStep === 'usecase' ? 25 :
                      currentStep === 'summary' ? 40 :
                      40 + ((currentChangeIndex + 1) / totalChangeGroups) * 60}%`
            }}
          />
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[400px]">
        {currentStep === 'overview' && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              {getStepIcon('overview')}
              <h3 className="text-lg font-medium">Overview</h3>
            </div>

            <AnalysisCard title="Welcome to your PR Walkthrough" className="border-l-4 border-l-blue-500">
              <div className="space-y-4">
                <p className="text-gray-700 dark:text-gray-300">
                  We&apos;ll walk through this PR step by step to help you understand the changes.
                  At each step, you can mark it as &quot;Makes sense&quot; to continue.
                </p>

                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">What we&apos;ll cover:</h4>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• <strong>Use Case:</strong> What problem this PR solves</li>
                    <li>• <strong>Summary:</strong> High-level overview of changes</li>
                    <li>• <strong>Changes:</strong> {totalChangeGroups} logical groups of changes to review</li>
                  </ul>
                </div>

                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-gray-500">
                    Ready to start?
                  </div>
                  <button
                    onClick={() => markStepComplete('overview')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Makes sense, let&apos;s go!</span>
                  </button>
                </div>
              </div>
            </AnalysisCard>
          </div>
        )}

        {/* Use Case Step */}
        {currentStep === 'usecase' && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              {getStepIcon('usecase')}
              <h3 className="text-lg font-medium">Use Case</h3>
            </div>

            <AnalysisCard title="What's the Use Case?" className="border-l-4 border-l-blue-500">
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="text-sm font-medium text-gray-600 mr-2">Type:</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">
                    {walkthrough.useCase.type.replace('_', ' ')}
                  </span>
                </div>

                <p className="text-gray-700 dark:text-gray-300">
                  {walkthrough.useCase.description}
                </p>

                {walkthrough.useCase.concreteExample && (
                  <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Concrete Example:
                    </p>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {walkthrough.useCase.concreteExample}
                    </p>
                  </div>
                )}

                {walkthrough.useCase.beforeAfter && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                      <p className="text-sm font-medium text-red-700 dark:text-red-400 mb-1">
                        Before:
                      </p>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {walkthrough.useCase.beforeAfter.before}
                      </p>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                      <p className="text-sm font-medium text-green-700 dark:text-green-400 mb-1">
                        After:
                      </p>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {walkthrough.useCase.beforeAfter.after}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-gray-500">
                    Does the use case make sense?
                  </div>
                  <button
                    onClick={() => markStepComplete('usecase')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Makes sense</span>
                  </button>
                </div>
              </div>
            </AnalysisCard>
          </div>
        )}

        {/* Summary Step */}
        {currentStep === 'summary' && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              {getStepIcon('summary')}
              <h3 className="text-lg font-medium">Summary</h3>
            </div>

            <AnalysisCard title="High-Level Overview" className="border-l-4 border-l-purple-500">
              <div className="space-y-4">
                <p className="text-gray-700 dark:text-gray-300">{walkthrough.summary}</p>

                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-md">
                  <h4 className="font-medium text-purple-900 dark:text-purple-100 mb-2">
                    Next: We&apos;ll review {totalChangeGroups} groups of changes
                  </h4>
                  <div className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                    {walkthrough.reviewOrder.slice(0, 3).map((groupId, index) => {
                      const group = walkthrough.changeGroups.find(g => g.id === groupId);
                      return group ? (
                        <div key={groupId} className="flex items-center space-x-2">
                          <span className="w-5 h-5 bg-purple-200 dark:bg-purple-700 rounded-full flex items-center justify-center text-xs font-medium">
                            {index + 1}
                          </span>
                          <span>{group.title}</span>
                        </div>
                      ) : null;
                    })}
                    {totalChangeGroups > 3 && (
                      <div className="text-xs text-purple-600 dark:text-purple-300 ml-7">
                        ...and {totalChangeGroups - 3} more
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-gray-500">
                    Ready to dive into the changes?
                  </div>
                  <button
                    onClick={() => markStepComplete('summary')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Makes sense</span>
                  </button>
                </div>
              </div>
            </AnalysisCard>
          </div>
        )}

        {/* Changes Step */}
        {currentStep === 'changes' && currentChangeGroup && (
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {getStepIcon('changes', currentChangeGroup.id)}
                <h3 className="text-lg font-medium">
                  Change {currentChangeIndex + 1} of {totalChangeGroups}
                </h3>
              </div>
              <div className="text-sm text-gray-500">
                {walkthrough.reviewOrder.slice(0, currentChangeIndex).filter(id => stepProgress.changes[id]).length} completed
              </div>
            </div>

            <AnalysisCard
              title={currentChangeGroup.title}
              className="border-l-4 border-l-orange-500"
            >
              <div className="space-y-4">
                <p className="text-gray-700 dark:text-gray-300">
                  {currentChangeGroup.description}
                </p>

                <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-md">
                  <p className="text-sm font-medium text-orange-700 dark:text-orange-400 mb-1">
                    Explanation:
                  </p>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {currentChangeGroup.explanation}
                  </p>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Files ({currentChangeGroup.files.length}):
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {currentChangeGroup.files.map((file, fileIndex) => (
                      <span
                        key={fileIndex}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-xs"
                      >
                        {file}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="text-sm text-gray-500">
                    {isLastStep() ? 'Does this final change make sense?' : 'Does this change make sense?'}
                  </div>
                  <button
                    onClick={() => markStepComplete('changes', currentChangeGroup.id)}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>Makes sense</span>
                  </button>
                </div>
              </div>
            </AnalysisCard>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between mt-6 pt-4 border-t">
        <button
          onClick={prevStep}
          disabled={!canGoPrev()}
          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          <ArrowLeftIcon className="w-4 h-4" />
          <span>Previous</span>
        </button>

        <div className="text-sm text-gray-500">
          {isLastStep() ? (
            <span className="text-green-600 font-medium">🎉 Walkthrough Complete!</span>
          ) : (
            <span>Mark the current step to continue</span>
          )}
        </div>

        <button
          onClick={nextStep}
          disabled={!canGoNext()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        >
          <span>Next</span>
          <ArrowRightIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
}
